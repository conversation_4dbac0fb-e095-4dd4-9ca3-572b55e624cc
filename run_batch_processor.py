#!/usr/bin/env python3
"""
Simple runner script for the batch PDF processor
"""

import os
import sys
from pathlib import Path
from batch_pdf_processor import BatchPDFProcessor
import config

def validate_config():
    """Validate configuration before running"""
    errors = []
    
    # Check API key
    if config.GEMINI_API_KEY == "YOUR_GEMINI_API_KEY_HERE":
        errors.append("Please set your Gemini API key in config.py")
    
    # Check PDF file
    if not Path(config.PDF_PATH).exists():
        errors.append(f"PDF file not found: {config.PDF_PATH}")
    
    if errors:
        print("Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

def main():
    """Main function"""
    print("Batch PDF Processor")
    print("=" * 50)
    
    # Validate configuration
    if not validate_config():
        print("\nPlease fix the configuration errors and try again.")
        sys.exit(1)
    
    # Display configuration
    print(f"PDF File: {config.PDF_PATH}")
    print(f"Batch Size: {config.BATCH_SIZE} pages")
    print(f"Start Page: {config.START_PAGE}")
    print(f"End Page: {config.END_PAGE or 'Last page'}")
    print(f"Images Directory: {config.IMAGES_DIR}")
    print(f"JSON Directory: {config.JSON_DIR}")
    
    # Confirm before starting
    response = input("\nDo you want to start processing? (y/N): ")
    if response.lower() != 'y':
        print("Processing cancelled.")
        sys.exit(0)
    
    try:
        # Initialize processor
        processor = BatchPDFProcessor(
            api_key=config.GEMINI_API_KEY,
            batch_size=config.BATCH_SIZE
        )
        
        # Process PDF in batches
        processor.process_pdf_in_batches(
            pdf_path=config.PDF_PATH,
            start_page=config.START_PAGE,
            end_page=config.END_PAGE
        )
        
        print("\n✅ Processing completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Processing interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during processing: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
