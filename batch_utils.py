#!/usr/bin/env python3
"""
Utility functions for batch PDF processing
"""

import os
import json
import re
from pathlib import Path
from typing import List, Dict, Tuple
import fitz

def get_pdf_info(pdf_path: str) -> Dict:
    """Get basic information about a PDF file"""
    doc = fitz.open(pdf_path)
    info = {
        "total_pages": len(doc),
        "metadata": doc.metadata,
        "file_size": os.path.getsize(pdf_path),
        "file_path": pdf_path
    }
    doc.close()
    return info

def list_completed_batches(json_dir: str = "json_outputs") -> List[Tuple[int, int, str]]:
    """
    List all completed batches by scanning JSON files.
    
    Returns:
        List of tuples (start_page, end_page, file_path)
    """
    json_path = Path(json_dir)
    if not json_path.exists():
        return []
    
    batches = []
    pattern = re.compile(r'JSON-Pg(\d+)-(\d+)\.json')
    
    for json_file in json_path.glob("JSON-Pg*.json"):
        match = pattern.match(json_file.name)
        if match:
            start_page = int(match.group(1))
            end_page = int(match.group(2))
            batches.append((start_page, end_page, str(json_file)))
    
    # Sort by start page
    batches.sort(key=lambda x: x[0])
    return batches

def find_next_batch_to_process(pdf_path: str, batch_size: int = 50, json_dir: str = "json_outputs") -> int:
    """
    Find the next batch that needs to be processed.
    
    Returns:
        Starting page number for the next batch to process
    """
    pdf_info = get_pdf_info(pdf_path)
    total_pages = pdf_info["total_pages"]
    
    completed_batches = list_completed_batches(json_dir)
    
    if not completed_batches:
        return 1  # Start from the beginning
    
    # Find gaps in completed batches
    expected_page = 1
    for start_page, end_page, _ in completed_batches:
        if start_page > expected_page:
            # Found a gap
            return expected_page
        expected_page = end_page + 1
    
    # No gaps found, return next page after last completed batch
    if expected_page <= total_pages:
        return expected_page
    else:
        return None  # All pages processed

def get_processing_progress(pdf_path: str, json_dir: str = "json_outputs") -> Dict:
    """Get detailed processing progress information"""
    pdf_info = get_pdf_info(pdf_path)
    total_pages = pdf_info["total_pages"]
    
    completed_batches = list_completed_batches(json_dir)
    
    # Calculate processed pages
    processed_pages = set()
    for start_page, end_page, _ in completed_batches:
        processed_pages.update(range(start_page, end_page + 1))
    
    progress = {
        "total_pages": total_pages,
        "processed_pages": len(processed_pages),
        "remaining_pages": total_pages - len(processed_pages),
        "completion_percentage": (len(processed_pages) / total_pages) * 100,
        "completed_batches": len(completed_batches),
        "batch_details": completed_batches,
        "missing_pages": []
    }
    
    # Find missing pages
    all_pages = set(range(1, total_pages + 1))
    missing_pages = sorted(all_pages - processed_pages)
    progress["missing_pages"] = missing_pages
    
    return progress

def validate_batch_json(json_file_path: str) -> Dict:
    """Validate a batch JSON file and return validation results"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        validation = {
            "valid": True,
            "file_path": json_file_path,
            "errors": [],
            "warnings": [],
            "stats": {}
        }
        
        # Check required structure
        if "batch_info" not in data:
            validation["errors"].append("Missing 'batch_info' section")
            validation["valid"] = False
        
        if "pages" not in data:
            validation["errors"].append("Missing 'pages' section")
            validation["valid"] = False
        
        if validation["valid"]:
            # Collect statistics
            batch_info = data["batch_info"]
            pages = data["pages"]
            
            validation["stats"] = {
                "start_page": batch_info.get("start_page"),
                "end_page": batch_info.get("end_page"),
                "expected_pages": batch_info.get("total_pages"),
                "actual_pages": len(pages),
                "successful_extractions": 0,
                "failed_extractions": 0
            }
            
            # Check each page
            for page_key, page_data in pages.items():
                if page_data.get("extraction_result", {}).get("success", False):
                    validation["stats"]["successful_extractions"] += 1
                else:
                    validation["stats"]["failed_extractions"] += 1
            
            # Warnings
            if validation["stats"]["actual_pages"] != validation["stats"]["expected_pages"]:
                validation["warnings"].append(
                    f"Page count mismatch: expected {validation['stats']['expected_pages']}, "
                    f"got {validation['stats']['actual_pages']}"
                )
            
            if validation["stats"]["failed_extractions"] > 0:
                validation["warnings"].append(
                    f"{validation['stats']['failed_extractions']} pages had extraction failures"
                )
        
        return validation
        
    except json.JSONDecodeError as e:
        return {
            "valid": False,
            "file_path": json_file_path,
            "errors": [f"JSON parsing error: {str(e)}"],
            "warnings": [],
            "stats": {}
        }
    except Exception as e:
        return {
            "valid": False,
            "file_path": json_file_path,
            "errors": [f"Validation error: {str(e)}"],
            "warnings": [],
            "stats": {}
        }

def print_progress_report(pdf_path: str, json_dir: str = "json_outputs"):
    """Print a detailed progress report"""
    print("PDF Processing Progress Report")
    print("=" * 50)
    
    # PDF info
    pdf_info = get_pdf_info(pdf_path)
    print(f"PDF File: {pdf_path}")
    print(f"Total Pages: {pdf_info['total_pages']}")
    print(f"File Size: {pdf_info['file_size']:,} bytes")
    
    # Progress info
    progress = get_processing_progress(pdf_path, json_dir)
    print(f"\nProgress:")
    print(f"  Processed Pages: {progress['processed_pages']}/{progress['total_pages']}")
    print(f"  Completion: {progress['completion_percentage']:.1f}%")
    print(f"  Completed Batches: {progress['completed_batches']}")
    
    if progress['missing_pages']:
        print(f"  Missing Pages: {len(progress['missing_pages'])} pages")
        if len(progress['missing_pages']) <= 20:
            print(f"    Pages: {progress['missing_pages']}")
        else:
            print(f"    First 10: {progress['missing_pages'][:10]}")
            print(f"    Last 10: {progress['missing_pages'][-10:]}")
    
    # Batch details
    print(f"\nCompleted Batches:")
    for start, end, file_path in progress['batch_details']:
        file_name = Path(file_path).name
        print(f"  {file_name}: Pages {start}-{end}")
    
    # Next batch
    next_batch = find_next_batch_to_process(pdf_path, json_dir=json_dir)
    if next_batch:
        print(f"\nNext batch to process: Starting from page {next_batch}")
    else:
        print(f"\n✅ All pages have been processed!")

def main():
    """Main function for running utilities"""
    import sys
    import config
    
    if len(sys.argv) < 2:
        print("Usage: python batch_utils.py <command>")
        print("Commands:")
        print("  progress - Show processing progress")
        print("  validate - Validate all JSON files")
        print("  next - Show next batch to process")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "progress":
        print_progress_report(config.PDF_PATH)
    
    elif command == "validate":
        print("Validating JSON files...")
        batches = list_completed_batches()
        for start, end, file_path in batches:
            validation = validate_batch_json(file_path)
            status = "✅" if validation["valid"] else "❌"
            print(f"{status} {Path(file_path).name}")
            if validation["errors"]:
                for error in validation["errors"]:
                    print(f"    Error: {error}")
            if validation["warnings"]:
                for warning in validation["warnings"]:
                    print(f"    Warning: {warning}")
    
    elif command == "next":
        next_batch = find_next_batch_to_process(config.PDF_PATH)
        if next_batch:
            print(f"Next batch should start from page: {next_batch}")
        else:
            print("All pages have been processed!")
    
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
