# Batch PDF Processor

A Python application that processes PDF documents in batches, converting pages to images and extracting structured data using Google's Gemini AI.

## Features

- **Batch Processing**: Process PDFs in configurable batches (default: 50 pages per batch)
- **Image Generation**: Convert PDF pages to high-quality PNG images
- **AI Data Extraction**: Use Gemini AI to extract structured data from document images
- **JSON Output**: Save results in organized JSON files named like `JSON-Pg1-50.json`, `JSON-Pg51-100.json`, etc.
- **Progress Tracking**: Monitor processing progress and resume from any point
- **Error Handling**: Robust error handling with detailed logging

## Directory Structure

```
├── batch_pdf_processor.py    # Main processing engine
├── run_batch_processor.py    # Simple runner script
├── batch_utils.py           # Utility functions for monitoring and validation
├── config.py               # Configuration file
├── requirements.txt        # Python dependencies
├── images/                # Generated PDF page images
└── json_outputs/          # Extracted data in JSON format
```

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Key**
   Edit `config.py` and replace `YOUR_GEMINI_API_KEY_HERE` with your actual Gemini API key:
   ```python
   GEMINI_API_KEY = "your_actual_api_key_here"
   ```

3. **Set PDF Path**
   In `config.py`, set the path to your PDF file:
   ```python
   PDF_PATH = "path/to/your/document.pdf"
   ```

## Usage

### Basic Usage

1. **Run the batch processor:**
   ```bash
   python run_batch_processor.py
   ```

2. **Monitor progress:**
   ```bash
   python batch_utils.py progress
   ```

3. **Validate JSON outputs:**
   ```bash
   python batch_utils.py validate
   ```

### Advanced Usage

#### Custom Batch Processing

```python
from batch_pdf_processor import BatchPDFProcessor

# Initialize with custom batch size
processor = BatchPDFProcessor(
    api_key="your_api_key",
    batch_size=25  # Process 25 pages per batch
)

# Process specific page range
processor.process_pdf_in_batches(
    pdf_path="document.pdf",
    start_page=101,  # Start from page 101
    end_page=200     # End at page 200
)
```

#### Resume Processing

```python
from batch_utils import find_next_batch_to_process

# Find where to resume
next_page = find_next_batch_to_process("document.pdf")
if next_page:
    processor.process_pdf_in_batches(
        pdf_path="document.pdf",
        start_page=next_page
    )
```

## Configuration Options

Edit `config.py` to customize:

- `BATCH_SIZE`: Number of pages per batch (default: 50)
- `START_PAGE`: Starting page number (default: 1)
- `END_PAGE`: Ending page number (default: None for last page)
- `IMAGE_QUALITY`: Image zoom factor (default: 2.0)
- `DELAY_BETWEEN_REQUESTS`: API request delay in seconds (default: 1)

## Output Format

### JSON Structure

Each batch JSON file contains:

```json
{
  "batch_info": {
    "start_page": 1,
    "end_page": 50,
    "total_pages": 50,
    "processing_timestamp": "2024-01-15 10:30:00"
  },
  "pages": {
    "page_1": {
      "page_number": 1,
      "image_path": "images/page_0001.png",
      "extraction_result": {
        "success": true,
        "data": {
          // Extracted structured data
        },
        "raw_response": "..."
      }
    }
    // ... more pages
  }
}
```

### File Naming Convention

- **Images**: `page_0001.png`, `page_0002.png`, etc.
- **JSON files**: `JSON-Pg1-50.json`, `JSON-Pg51-100.json`, etc.

## Utility Commands

### Check Progress
```bash
python batch_utils.py progress
```
Shows:
- Total pages processed
- Completion percentage
- Missing pages
- Next batch to process

### Validate JSON Files
```bash
python batch_utils.py validate
```
Validates all JSON files and reports:
- File integrity
- Data structure validation
- Extraction success rates

### Find Next Batch
```bash
python batch_utils.py next
```
Shows the next page number to start processing from.

## Error Handling

The system handles various error scenarios:

- **API Rate Limiting**: Automatic delays between requests
- **Network Issues**: Retry logic for failed API calls
- **Invalid JSON**: Graceful handling of malformed responses
- **File I/O Errors**: Proper error reporting and recovery

## Troubleshooting

### Common Issues

1. **API Key Error**
   - Ensure your Gemini API key is correctly set in `config.py`
   - Verify the API key has proper permissions

2. **PDF Not Found**
   - Check the PDF path in `config.py`
   - Ensure the file exists and is readable

3. **Memory Issues**
   - Reduce batch size in `config.py`
   - Process smaller page ranges

4. **Rate Limiting**
   - Increase `DELAY_BETWEEN_REQUESTS` in `config.py`
   - Process during off-peak hours

### Resume Interrupted Processing

If processing is interrupted:

1. Check progress: `python batch_utils.py progress`
2. Find next batch: `python batch_utils.py next`
3. Resume from that page using the custom processing script

## Performance Tips

- **Batch Size**: Larger batches are more efficient but use more memory
- **Image Quality**: Higher quality improves extraction but increases processing time
- **Parallel Processing**: For very large documents, consider running multiple instances on different page ranges
- **API Limits**: Monitor your Gemini API usage and quotas

## License

This project is provided as-is for educational and development purposes.
