"""
Configuration file for batch PDF processor
"""

# Gemini API Configuration
GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"  # Replace with your actual API key

# Processing Configuration
BATCH_SIZE = 50  # Number of pages to process in each batch
PDF_PATH = "your_document.pdf"  # Path to your PDF file

# Optional: Specify page range
START_PAGE = 1  # Starting page number (1-indexed)
END_PAGE = None  # Ending page number (None for last page)

# Directory Configuration
IMAGES_DIR = "images"
JSON_DIR = "json_outputs"

# Processing Options
IMAGE_QUALITY = 2.0  # Zoom factor for PDF to image conversion (higher = better quality)
DELAY_BETWEEN_REQUESTS = 1  # Seconds to wait between API requests to avoid rate limiting
