#!/usr/bin/env python3
"""
PDF to Images Converter - Batch Processing

This script converts PDF pages to images in batches of 50 pages.
Images are saved with sequential naming: page_0001.png, page_0002.png, etc.
"""

import os
import fitz  # PyMuPDF
from pathlib import Path
import argparse

class PDFToImagesConverter:
    def __init__(self, pdf_path: str, output_dir: str = "images", batch_size: int = 50):
        """
        Initialize the PDF to Images converter.
        
        Args:
            pdf_path: Path to the PDF file
            output_dir: Directory to save images (default: "images")
            batch_size: Number of pages to process in each batch (default: 50)
        """
        self.pdf_path = pdf_path
        self.output_dir = Path(output_dir)
        self.batch_size = batch_size
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        # Open PDF and get total pages
        self.doc = fitz.open(pdf_path)
        self.total_pages = len(self.doc)
        
        print(f"PDF loaded: {pdf_path}")
        print(f"Total pages: {self.total_pages}")
        print(f"Output directory: {self.output_dir}")
        print(f"Batch size: {batch_size}")
    
    def convert_page_to_image(self, page_num: int, zoom_factor: float = 2.0) -> str:
        """
        Convert a single PDF page to image.
        
        Args:
            page_num: Page number (0-indexed)
            zoom_factor: Zoom factor for image quality (default: 2.0)
            
        Returns:
            Path to the saved image file
        """
        page = self.doc.load_page(page_num)
        
        # Convert page to image with zoom for better quality
        mat = fitz.Matrix(zoom_factor, zoom_factor)
        pix = page.get_pixmap(matrix=mat)
        
        # Save image with sequential naming
        image_filename = f"page_{page_num + 1:04d}.png"
        image_path = self.output_dir / image_filename
        pix.save(str(image_path))
        
        return str(image_path)
    
    def convert_batch(self, start_page: int, end_page: int) -> list:
        """
        Convert a batch of pages to images.
        
        Args:
            start_page: Starting page number (1-indexed)
            end_page: Ending page number (1-indexed, inclusive)
            
        Returns:
            List of image file paths
        """
        image_paths = []
        
        print(f"\nConverting batch: Pages {start_page} to {end_page}")
        print("-" * 50)
        
        for page_num in range(start_page - 1, min(end_page, self.total_pages)):
            image_path = self.convert_page_to_image(page_num)
            image_paths.append(image_path)
            print(f"  ✓ Page {page_num + 1} -> {Path(image_path).name}")
        
        print(f"Batch completed: {len(image_paths)} images saved")
        return image_paths
    
    def find_next_batch_to_convert(self) -> tuple:
        """
        Find the next batch of pages that need to be converted.

        Returns:
            Tuple of (start_page, end_page) for next batch, or (None, None) if all done
        """
        # Check existing images to find what's already converted
        existing_images = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            existing_images.extend(self.output_dir.glob(ext))

        if not existing_images:
            # No images exist, start from page 1
            end_page = min(self.batch_size, self.total_pages)
            return (1, end_page)

        # Find highest page number already converted
        max_page = 0
        for img_path in existing_images:
            # Extract page number from filename like "page_0001.png"
            try:
                page_num = int(img_path.stem.split('_')[1])
                max_page = max(max_page, page_num)
            except (IndexError, ValueError):
                continue

        # Calculate next batch
        next_start = max_page + 1
        if next_start > self.total_pages:
            return (None, None)  # All pages converted

        next_end = min(next_start + self.batch_size - 1, self.total_pages)
        return (next_start, next_end)

    def convert_next_batch(self):
        """
        Convert the next batch of 50 pages automatically.
        """
        start_page, end_page = self.find_next_batch_to_convert()

        if start_page is None:
            print(f"\n✅ ALL PAGES ALREADY CONVERTED!")
            print(f"Total pages in PDF: {self.total_pages}")
            print(f"All images are in: {self.output_dir}")
            return

        print(f"\n{'='*60}")
        print(f"CONVERTING NEXT BATCH OF PAGES")
        print(f"{'='*60}")
        print(f"PDF: {self.pdf_path}")
        print(f"Total pages in PDF: {self.total_pages}")
        print(f"Converting pages {start_page} to {end_page} ({end_page - start_page + 1} pages)")

        # Convert this batch
        image_paths = self.convert_batch(start_page, end_page)

        print(f"\n{'='*60}")
        print(f"✅ BATCH COMPLETED!")
        print(f"{'='*60}")
        print(f"Converted: {len(image_paths)} images")
        print(f"Pages {start_page} to {end_page}")
        print(f"Images saved in: {self.output_dir}")

        # Show what to do next
        next_start, next_end = self.find_next_batch_to_convert()
        if next_start is not None:
            print(f"\n📋 NEXT STEPS:")
            print(f"Run the script again to convert pages {next_start} to {next_end}")
            print(f"Command: python pdf_to_images.py {self.pdf_path}")
        else:
            print(f"\n🎉 ALL PAGES CONVERTED!")
            print(f"Ready for data extraction with:")
            print(f"python extract_data_from_images.py YOUR_API_KEY")

    def convert_all_pages(self, start_page: int = 1, end_page: int = None):
        """
        Convert all pages to images in batches.

        Args:
            start_page: Starting page number (1-indexed, default: 1)
            end_page: Ending page number (1-indexed, inclusive, default: last page)
        """
        if end_page is None:
            end_page = self.total_pages

        print(f"\n{'='*60}")
        print(f"CONVERTING PDF TO IMAGES")
        print(f"{'='*60}")
        print(f"Processing pages {start_page} to {end_page} in batches of {self.batch_size}")

        current_page = start_page
        batch_number = 1
        total_images = 0

        while current_page <= end_page:
            batch_end = min(current_page + self.batch_size - 1, end_page)

            print(f"\n🔄 BATCH {batch_number}: Pages {current_page} to {batch_end}")

            # Convert this batch
            image_paths = self.convert_batch(current_page, batch_end)
            total_images += len(image_paths)

            # Move to next batch
            current_page = batch_end + 1
            batch_number += 1

        print(f"\n{'='*60}")
        print(f"✅ CONVERSION COMPLETED!")
        print(f"{'='*60}")
        print(f"Total images created: {total_images}")
        print(f"Images saved in: {self.output_dir}")
        print(f"Batches processed: {batch_number - 1}")

        # Show batch ranges for reference
        print(f"\nBatch ranges for JSON processing:")
        current_page = start_page
        batch_num = 1
        while current_page <= end_page:
            batch_end = min(current_page + self.batch_size - 1, end_page)
            print(f"  Batch {batch_num}: Pages {current_page}-{batch_end} -> JSON-Pg{current_page}-{batch_end}.json")
            current_page = batch_end + 1
            batch_num += 1
    
    def get_batch_info(self, start_page: int = 1, end_page: int = None):
        """
        Get information about batches without converting.
        
        Args:
            start_page: Starting page number (1-indexed, default: 1)
            end_page: Ending page number (1-indexed, inclusive, default: last page)
        """
        if end_page is None:
            end_page = self.total_pages
        
        print(f"\nBatch Information:")
        print(f"PDF: {self.pdf_path}")
        print(f"Total pages: {self.total_pages}")
        print(f"Processing range: {start_page} to {end_page}")
        print(f"Batch size: {self.batch_size}")
        
        current_page = start_page
        batch_number = 1
        
        print(f"\nBatch breakdown:")
        while current_page <= end_page:
            batch_end = min(current_page + self.batch_size - 1, end_page)
            pages_in_batch = batch_end - current_page + 1
            print(f"  Batch {batch_number}: Pages {current_page}-{batch_end} ({pages_in_batch} pages)")
            current_page = batch_end + 1
            batch_number += 1
        
        total_batches = batch_number - 1
        print(f"\nTotal batches: {total_batches}")
    
    def close(self):
        """Close the PDF document"""
        if hasattr(self, 'doc'):
            self.doc.close()

# Default PDF file path
DEFAULT_PDF_PATH = "NEC_2023.pdf"

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description="Convert PDF pages to images in batches of 50")
    parser.add_argument("pdf_path", nargs='?', default=DEFAULT_PDF_PATH, help="Path to the PDF file (default: NEC_2023.pdf)")
    parser.add_argument("--output-dir", default="images", help="Output directory for images (default: images)")
    parser.add_argument("--batch-size", type=int, default=50, help="Batch size (default: 50)")
    parser.add_argument("--start-page", type=int, help="Starting page number (for custom range)")
    parser.add_argument("--end-page", type=int, help="Ending page number (for custom range)")
    parser.add_argument("--all", action="store_true", help="Convert all pages at once (not recommended for large PDFs)")
    parser.add_argument("--info-only", action="store_true", help="Show batch info without converting")

    args = parser.parse_args()

    # Check if PDF file exists
    if not os.path.exists(args.pdf_path):
        print(f"❌ Error: PDF file not found: {args.pdf_path}")
        return

    try:
        # Initialize converter
        converter = PDFToImagesConverter(
            pdf_path=args.pdf_path,
            output_dir=args.output_dir,
            batch_size=args.batch_size
        )

        if args.info_only:
            # Show batch information only
            start_page = args.start_page or 1
            converter.get_batch_info(start_page, args.end_page)
        elif args.all or (args.start_page and args.end_page):
            # Convert all pages or custom range
            start_page = args.start_page or 1
            converter.convert_all_pages(start_page, args.end_page)
        else:
            # Default behavior: Convert next batch of 50 pages
            converter.convert_next_batch()

        # Close PDF
        converter.close()

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
