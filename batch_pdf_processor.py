import os
import json
import base64
from pathlib import Path
import fitz  # PyMuPDF
import google.generativeai as genai
from PIL import Image
import io
import time
from typing import List, Dict, Any

class BatchPDFProcessor:
    def __init__(self, api_key: str, batch_size: int = 50):
        """
        Initialize the batch PDF processor.
        
        Args:
            api_key: Google Gemini API key
            batch_size: Number of pages to process in each batch (default: 50)
        """
        self.api_key = api_key
        self.batch_size = batch_size
        self.setup_gemini()
        
        # Create directories
        self.images_dir = Path("images")
        self.json_dir = Path("json_outputs")
        self.images_dir.mkdir(exist_ok=True)
        self.json_dir.mkdir(exist_ok=True)
    
    def setup_gemini(self):
        """Configure Gemini API"""
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
    
    def pdf_to_images_batch(self, pdf_path: str, start_page: int, end_page: int) -> List[str]:
        """
        Convert a range of PDF pages to images.
        
        Args:
            pdf_path: Path to the PDF file
            start_page: Starting page number (0-indexed)
            end_page: Ending page number (0-indexed, exclusive)
            
        Returns:
            List of image file paths
        """
        doc = fitz.open(pdf_path)
        image_paths = []
        
        print(f"Converting pages {start_page + 1} to {min(end_page, len(doc))} to images...")
        
        for page_num in range(start_page, min(end_page, len(doc))):
            page = doc.load_page(page_num)
            
            # Convert page to image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            
            # Save image
            image_filename = f"page_{page_num + 1:04d}.png"
            image_path = self.images_dir / image_filename
            pix.save(str(image_path))
            image_paths.append(str(image_path))
            
            print(f"  Saved page {page_num + 1} as {image_filename}")
        
        doc.close()
        return image_paths
    
    def encode_image(self, image_path: str) -> str:
        """Encode image to base64 string"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def process_image_with_gemini(self, image_path: str) -> Dict[str, Any]:
        """
        Process a single image with Gemini API to extract structured data.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary containing extracted data
        """
        try:
            # Load and prepare image
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # Create the prompt for data extraction
            prompt = """
            Analyze this document image and extract all structured information in JSON format.
            
            Please extract:
            1. All text content organized by sections/headings
            2. Any tables with their data
            3. Key-value pairs (like forms, specifications, etc.)
            4. Lists and bullet points
            5. Any numerical data, dates, or measurements
            6. Document metadata if visible (title, page numbers, etc.)
            
            Format the response as a clean JSON object with appropriate nested structure.
            If there are tables, represent them as arrays of objects.
            If there are forms or key-value pairs, use appropriate object structures.
            
            Return only the JSON object, no additional text.
            """
            
            # Process with Gemini
            response = self.model.generate_content([prompt, {"mime_type": "image/png", "data": image_data}])
            
            # Parse the response
            response_text = response.text.strip()
            
            # Try to parse as JSON
            try:
                # Remove any markdown code block formatting if present
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                
                extracted_data = json.loads(response_text)
                return {
                    "success": True,
                    "data": extracted_data,
                    "raw_response": response.text
                }
            except json.JSONDecodeError as e:
                return {
                    "success": False,
                    "error": f"JSON parsing error: {str(e)}",
                    "raw_response": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Processing error: {str(e)}",
                "raw_response": None
            }
    
    def process_batch(self, pdf_path: str, batch_start: int, batch_end: int) -> Dict[str, Any]:
        """
        Process a batch of PDF pages.
        
        Args:
            pdf_path: Path to the PDF file
            batch_start: Starting page number (1-indexed)
            batch_end: Ending page number (1-indexed, inclusive)
            
        Returns:
            Dictionary containing batch processing results
        """
        print(f"\n=== Processing Batch: Pages {batch_start} to {batch_end} ===")
        
        # Convert pages to images (convert to 0-indexed for internal processing)
        image_paths = self.pdf_to_images_batch(pdf_path, batch_start - 1, batch_end)
        
        batch_results = {
            "batch_info": {
                "start_page": batch_start,
                "end_page": batch_end,
                "total_pages": len(image_paths),
                "processing_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "pages": {}
        }
        
        # Process each image with Gemini
        for i, image_path in enumerate(image_paths):
            page_num = batch_start + i
            print(f"Processing page {page_num} with Gemini API...")
            
            result = self.process_image_with_gemini(image_path)
            batch_results["pages"][f"page_{page_num}"] = {
                "page_number": page_num,
                "image_path": image_path,
                "extraction_result": result
            }
            
            # Add a small delay to avoid rate limiting
            time.sleep(1)
        
        return batch_results
    
    def save_batch_json(self, batch_results: Dict[str, Any], batch_start: int, batch_end: int):
        """Save batch results to JSON file"""
        json_filename = f"JSON-Pg{batch_start}-{batch_end}.json"
        json_path = self.json_dir / json_filename
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False)
        
        print(f"Saved batch results to {json_filename}")
        return str(json_path)
    
    def process_pdf_in_batches(self, pdf_path: str, start_page: int = 1, end_page: int = None):
        """
        Process entire PDF in batches.
        
        Args:
            pdf_path: Path to the PDF file
            start_page: Starting page number (1-indexed, default: 1)
            end_page: Ending page number (1-indexed, inclusive, default: last page)
        """
        # Get total pages
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        doc.close()
        
        if end_page is None:
            end_page = total_pages
        
        print(f"PDF has {total_pages} pages. Processing pages {start_page} to {end_page} in batches of {self.batch_size}")
        
        # Process in batches
        current_page = start_page
        batch_number = 1
        
        while current_page <= end_page:
            batch_end = min(current_page + self.batch_size - 1, end_page)
            
            print(f"\n{'='*60}")
            print(f"BATCH {batch_number}: Pages {current_page} to {batch_end}")
            print(f"{'='*60}")
            
            # Process this batch
            batch_results = self.process_batch(pdf_path, current_page, batch_end)
            
            # Save batch results
            json_path = self.save_batch_json(batch_results, current_page, batch_end)
            
            print(f"Batch {batch_number} completed. Results saved to {json_path}")
            
            # Move to next batch
            current_page = batch_end + 1
            batch_number += 1
        
        print(f"\n{'='*60}")
        print("ALL BATCHES COMPLETED!")
        print(f"Processed {end_page - start_page + 1} pages in {batch_number - 1} batches")
        print(f"Images saved in: {self.images_dir}")
        print(f"JSON files saved in: {self.json_dir}")
        print(f"{'='*60}")

def main():
    """Main function to run the batch processor"""
    # Configuration
    API_KEY = "YOUR_GEMINI_API_KEY_HERE"  # Replace with your actual API key
    PDF_PATH = "your_document.pdf"  # Replace with your PDF path
    BATCH_SIZE = 50  # Process 50 pages per batch
    
    # Initialize processor
    processor = BatchPDFProcessor(api_key=API_KEY, batch_size=BATCH_SIZE)
    
    # Process PDF in batches
    processor.process_pdf_in_batches(PDF_PATH)

if __name__ == "__main__":
    main()
