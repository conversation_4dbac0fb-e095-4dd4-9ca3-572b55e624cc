#!/usr/bin/env python3
"""
Example usage of the Batch PDF Processor

This script demonstrates various ways to use the batch processing system.
"""

from batch_pdf_processor import BatchPDFProcessor
from batch_utils import (
    get_pdf_info, 
    get_processing_progress, 
    find_next_batch_to_process,
    print_progress_report
)
import config

def example_basic_processing():
    """Example: Basic batch processing"""
    print("=== Example 1: Basic Batch Processing ===")
    
    # Initialize processor
    processor = BatchPDFProcessor(
        api_key=config.GEMINI_API_KEY,
        batch_size=config.BATCH_SIZE
    )
    
    # Process entire PDF
    processor.process_pdf_in_batches(config.PDF_PATH)

def example_custom_range():
    """Example: Process specific page range"""
    print("=== Example 2: Custom Page Range ===")
    
    processor = BatchPDFProcessor(
        api_key=config.GEMINI_API_KEY,
        batch_size=25  # Smaller batches
    )
    
    # Process pages 1-100 only
    processor.process_pdf_in_batches(
        pdf_path=config.PDF_PATH,
        start_page=1,
        end_page=100
    )

def example_resume_processing():
    """Example: Resume interrupted processing"""
    print("=== Example 3: Resume Processing ===")
    
    # Find where to resume
    next_page = find_next_batch_to_process(config.PDF_PATH)
    
    if next_page:
        print(f"Resuming from page {next_page}")
        
        processor = BatchPDFProcessor(
            api_key=config.GEMINI_API_KEY,
            batch_size=config.BATCH_SIZE
        )
        
        processor.process_pdf_in_batches(
            pdf_path=config.PDF_PATH,
            start_page=next_page
        )
    else:
        print("All pages have been processed!")

def example_progress_monitoring():
    """Example: Monitor processing progress"""
    print("=== Example 4: Progress Monitoring ===")
    
    # Get PDF info
    pdf_info = get_pdf_info(config.PDF_PATH)
    print(f"PDF: {pdf_info['total_pages']} pages")
    
    # Get progress
    progress = get_processing_progress(config.PDF_PATH)
    print(f"Processed: {progress['processed_pages']}/{progress['total_pages']} pages")
    print(f"Completion: {progress['completion_percentage']:.1f}%")
    
    # Print detailed report
    print_progress_report(config.PDF_PATH)

def example_batch_by_batch():
    """Example: Process one batch at a time with custom logic"""
    print("=== Example 5: Batch-by-Batch Processing ===")
    
    processor = BatchPDFProcessor(
        api_key=config.GEMINI_API_KEY,
        batch_size=10  # Small batches for demonstration
    )
    
    pdf_info = get_pdf_info(config.PDF_PATH)
    total_pages = pdf_info['total_pages']
    
    current_page = 1
    batch_size = 10
    
    while current_page <= total_pages:
        batch_end = min(current_page + batch_size - 1, total_pages)
        
        print(f"\nProcessing batch: pages {current_page} to {batch_end}")
        
        # Process this batch
        batch_results = processor.process_batch(
            pdf_path=config.PDF_PATH,
            batch_start=current_page,
            batch_end=batch_end
        )
        
        # Save results
        json_path = processor.save_batch_json(
            batch_results, 
            current_page, 
            batch_end
        )
        
        print(f"Batch completed. Results saved to {json_path}")
        
        # Custom logic here - e.g., validate results, send notifications, etc.
        successful_pages = sum(
            1 for page_data in batch_results["pages"].values()
            if page_data["extraction_result"]["success"]
        )
        
        print(f"Success rate: {successful_pages}/{batch_end - current_page + 1} pages")
        
        # Move to next batch
        current_page = batch_end + 1

def example_error_handling():
    """Example: Custom error handling"""
    print("=== Example 6: Custom Error Handling ===")
    
    try:
        processor = BatchPDFProcessor(
            api_key=config.GEMINI_API_KEY,
            batch_size=config.BATCH_SIZE
        )
        
        # Process with custom error handling
        processor.process_pdf_in_batches(config.PDF_PATH)
        
    except FileNotFoundError:
        print("Error: PDF file not found. Please check the path in config.py")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        print("Check your API key and network connection")

def main():
    """Main function to run examples"""
    print("Batch PDF Processor - Usage Examples")
    print("=" * 50)
    
    # Check configuration
    if config.GEMINI_API_KEY == "YOUR_GEMINI_API_KEY_HERE":
        print("❌ Please set your Gemini API key in config.py")
        return
    
    if not config.PDF_PATH or config.PDF_PATH == "your_document.pdf":
        print("❌ Please set your PDF path in config.py")
        return
    
    print("Choose an example to run:")
    print("1. Basic batch processing")
    print("2. Custom page range")
    print("3. Resume processing")
    print("4. Progress monitoring")
    print("5. Batch-by-batch processing")
    print("6. Error handling")
    print("0. Exit")
    
    try:
        choice = input("\nEnter your choice (0-6): ").strip()
        
        if choice == "1":
            example_basic_processing()
        elif choice == "2":
            example_custom_range()
        elif choice == "3":
            example_resume_processing()
        elif choice == "4":
            example_progress_monitoring()
        elif choice == "5":
            example_batch_by_batch()
        elif choice == "6":
            example_error_handling()
        elif choice == "0":
            print("Goodbye!")
        else:
            print("Invalid choice. Please try again.")
            
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        print(f"\nError: {str(e)}")

if __name__ == "__main__":
    main()
