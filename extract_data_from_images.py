#!/usr/bin/env python3
"""
Data Extraction from Images using Gemini AI - Batch Processing

This script processes images in batches of 50 and extracts structured data using Gemini AI.
Creates JSON files named: JSON-Pg1-50.json, JSON-Pg51-100.json, etc.
"""

import os
import json
import time
from pathlib import Path
import google.generativeai as genai
import argparse
from typing import List, Dict, Any

class ImageDataExtractor:
    def __init__(self, api_key: str, images_dir: str = "images", output_dir: str = "json_outputs", batch_size: int = 50):
        """
        Initialize the Image Data Extractor.
        
        Args:
            api_key: Google Gemini API key
            images_dir: Directory containing images (default: "images")
            output_dir: Directory to save JSON files (default: "json_outputs")
            batch_size: Number of images to process in each batch (default: 50)
        """
        self.api_key = api_key
        self.images_dir = Path(images_dir)
        self.output_dir = Path(output_dir)
        self.batch_size = batch_size
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        # Setup Gemini API
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        print(f"Images directory: {self.images_dir}")
        print(f"Output directory: {self.output_dir}")
        print(f"Batch size: {batch_size}")
    
    def get_image_files(self) -> List[str]:
        """Get sorted list of image files"""
        image_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            image_files.extend(self.images_dir.glob(ext))
        
        # Sort by filename to ensure correct order
        image_files.sort(key=lambda x: x.name)
        return [str(f) for f in image_files]
    
    def extract_data_from_image(self, image_path: str, custom_prompt: str = None) -> Dict[str, Any]:
        """
        Extract data from a single image using Gemini AI.
        
        Args:
            image_path: Path to the image file
            custom_prompt: Custom prompt for data extraction
            
        Returns:
            Dictionary containing extraction results
        """
        try:
            # Default prompt if none provided
            if custom_prompt is None:
                prompt = """
                Analyze this document image and extract all structured information in JSON format.
                
                Please extract:
                1. All text content organized by sections/headings
                2. Any tables with their data
                3. Key-value pairs (like forms, specifications, etc.)
                4. Lists and bullet points
                5. Any numerical data, dates, or measurements
                6. Document metadata if visible (title, page numbers, etc.)
                
                Format the response as a clean JSON object with appropriate nested structure.
                If there are tables, represent them as arrays of objects.
                If there are forms or key-value pairs, use appropriate object structures.
                
                Return only the JSON object, no additional text.
                """
            else:
                prompt = custom_prompt
            
            # Load and process image
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # Process with Gemini
            response = self.model.generate_content([prompt, {"mime_type": "image/png", "data": image_data}])
            
            # Parse response
            response_text = response.text.strip()
            
            # Clean up response (remove markdown formatting if present)
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            
            # Try to parse as JSON
            try:
                extracted_data = json.loads(response_text)
                return {
                    "success": True,
                    "data": extracted_data,
                    "raw_response": response.text
                }
            except json.JSONDecodeError as e:
                return {
                    "success": False,
                    "error": f"JSON parsing error: {str(e)}",
                    "raw_response": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Processing error: {str(e)}",
                "raw_response": None
            }
    
    def process_batch(self, image_files: List[str], batch_start_page: int, batch_end_page: int, custom_prompt: str = None) -> Dict[str, Any]:
        """
        Process a batch of images.
        
        Args:
            image_files: List of image file paths for this batch
            batch_start_page: Starting page number for this batch
            batch_end_page: Ending page number for this batch
            custom_prompt: Custom prompt for data extraction
            
        Returns:
            Dictionary containing batch processing results
        """
        print(f"\n🔄 Processing Batch: Pages {batch_start_page} to {batch_end_page}")
        print("-" * 60)
        
        batch_results = {
            "batch_info": {
                "start_page": batch_start_page,
                "end_page": batch_end_page,
                "total_pages": len(image_files),
                "processing_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "pages": {}
        }
        
        # Process each image
        for i, image_path in enumerate(image_files):
            page_num = batch_start_page + i
            print(f"  Processing page {page_num}: {Path(image_path).name}")
            
            # Extract data using Gemini
            result = self.extract_data_from_image(image_path, custom_prompt)
            
            batch_results["pages"][f"page_{page_num}"] = {
                "page_number": page_num,
                "image_path": image_path,
                "extraction_result": result
            }
            
            # Show result status
            status = "✅" if result["success"] else "❌"
            print(f"    {status} Page {page_num} processed")
            
            # Add delay to avoid rate limiting
            time.sleep(1)
        
        return batch_results
    
    def save_batch_json(self, batch_results: Dict[str, Any], batch_start_page: int, batch_end_page: int) -> str:
        """
        Save batch results to JSON file with the specified naming convention.
        
        Args:
            batch_results: Batch processing results
            batch_start_page: Starting page number
            batch_end_page: Ending page number
            
        Returns:
            Path to the saved JSON file
        """
        json_filename = f"JSON-Pg{batch_start_page}-{batch_end_page}.json"
        json_path = self.output_dir / json_filename
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False)
        
        print(f"  💾 Saved: {json_filename}")
        return str(json_path)
    
    def process_all_images(self, start_page: int = 1, end_page: int = None, custom_prompt: str = None):
        """
        Process all images in batches.
        
        Args:
            start_page: Starting page number (1-indexed, default: 1)
            end_page: Ending page number (1-indexed, inclusive, default: last page)
            custom_prompt: Custom prompt for data extraction
        """
        # Get all image files
        all_image_files = self.get_image_files()
        
        if not all_image_files:
            print("❌ No image files found in the images directory!")
            return
        
        total_images = len(all_image_files)
        
        if end_page is None:
            end_page = total_images
        
        # Validate page range
        if start_page < 1 or start_page > total_images:
            print(f"❌ Invalid start page: {start_page}. Must be between 1 and {total_images}")
            return
        
        if end_page > total_images:
            end_page = total_images
        
        print(f"\n{'='*60}")
        print(f"EXTRACTING DATA FROM IMAGES")
        print(f"{'='*60}")
        print(f"Total images available: {total_images}")
        print(f"Processing pages {start_page} to {end_page} in batches of {self.batch_size}")
        
        # Process in batches
        current_page = start_page
        batch_number = 1
        total_processed = 0
        
        while current_page <= end_page:
            batch_end_page = min(current_page + self.batch_size - 1, end_page)
            
            # Get image files for this batch
            batch_image_files = all_image_files[current_page - 1:batch_end_page]
            
            print(f"\n📦 BATCH {batch_number}: Pages {current_page} to {batch_end_page}")
            
            # Process this batch
            batch_results = self.process_batch(
                batch_image_files, 
                current_page, 
                batch_end_page, 
                custom_prompt
            )
            
            # Save batch results
            json_path = self.save_batch_json(batch_results, current_page, batch_end_page)
            
            # Calculate success rate
            successful = sum(
                1 for page_data in batch_results["pages"].values()
                if page_data["extraction_result"]["success"]
            )
            total_in_batch = len(batch_image_files)
            success_rate = (successful / total_in_batch) * 100
            
            print(f"  📊 Batch {batch_number} completed: {successful}/{total_in_batch} successful ({success_rate:.1f}%)")
            
            total_processed += total_in_batch
            current_page = batch_end_page + 1
            batch_number += 1
        
        print(f"\n{'='*60}")
        print(f"✅ DATA EXTRACTION COMPLETED!")
        print(f"{'='*60}")
        print(f"Total images processed: {total_processed}")
        print(f"Batches created: {batch_number - 1}")
        print(f"JSON files saved in: {self.output_dir}")
        print(f"{'='*60}")

# Your Google Gemini API Key
GOOGLE_API_KEY = "AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k"

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description="Extract data from images using Gemini AI in batches")
    parser.add_argument("--api-key", default=GOOGLE_API_KEY, help="Gemini API key (default: uses built-in key)")
    parser.add_argument("--images-dir", default="images", help="Directory containing images (default: images)")
    parser.add_argument("--output-dir", default="json_outputs", help="Output directory for JSON files (default: json_outputs)")
    parser.add_argument("--batch-size", type=int, default=50, help="Batch size (default: 50)")
    parser.add_argument("--start-page", type=int, default=1, help="Starting page number (default: 1)")
    parser.add_argument("--end-page", type=int, help="Ending page number (default: last page)")
    parser.add_argument("--prompt-file", help="File containing custom prompt for data extraction")

    args = parser.parse_args()
    
    # Load custom prompt if provided
    custom_prompt = None
    if args.prompt_file:
        if os.path.exists(args.prompt_file):
            with open(args.prompt_file, 'r', encoding='utf-8') as f:
                custom_prompt = f.read().strip()
            print(f"Loaded custom prompt from: {args.prompt_file}")
        else:
            print(f"❌ Prompt file not found: {args.prompt_file}")
            return
    
    # Check if images directory exists
    if not os.path.exists(args.images_dir):
        print(f"❌ Images directory not found: {args.images_dir}")
        return
    
    try:
        # Initialize extractor
        extractor = ImageDataExtractor(
            api_key=args.api_key,
            images_dir=args.images_dir,
            output_dir=args.output_dir,
            batch_size=args.batch_size
        )
        
        # Process images
        extractor.process_all_images(
            start_page=args.start_page,
            end_page=args.end_page,
            custom_prompt=custom_prompt
        )
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    # Example usage if run without arguments
    import sys
    
    if len(sys.argv) == 1:
        print("Image Data Extractor using Gemini AI")
        print("=" * 40)
        print("\n🎯 SIMPLE USAGE (API key already configured):")
        print("  python extract_data_from_images.py")
        print("  → Uses built-in API key, processes all images")
        print("\n📋 USAGE EXAMPLES:")
        print("  python extract_data_from_images.py                           # Process all images")
        print("  python extract_data_from_images.py --batch-size 25           # Use 25 images per batch")
        print("  python extract_data_from_images.py --start-page 1 --end-page 100  # Process specific range")
        print("  python extract_data_from_images.py --prompt-file custom_prompt.txt # Use custom prompt")
        print("  python extract_data_from_images.py --api-key YOUR_KEY        # Use different API key")
        print("\nFor help: python extract_data_from_images.py --help")
        print(f"\n✅ API Key configured: {GOOGLE_API_KEY[:20]}...")
    else:
        main()
