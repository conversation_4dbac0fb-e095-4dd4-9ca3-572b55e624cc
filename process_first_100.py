"""
Process the first 100 images of the NEC document with Gemini AI.
This script uses the updated JSON cleaning functionality.
"""

import os
import time
from nec_gemini_processor import NECGeminiProcessor

def main():
    """Process the first 100 images."""
    print("🚀 Starting NEC Processing - First 100 Images")
    print("=" * 50)
    
    # Configuration
    API_KEY = "AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k"
    
    # Initialize processor
    print("🔧 Initializing Gemini processor...")
    processor = NECGeminiProcessor(API_KEY)
    
    # Processing parameters
    images_folder = "images"
    output_file = "nec_first_100_results.json"
    start_page = 1
    end_page = 100
    delay_seconds = 1.5  # Slightly longer delay for stability
    
    print(f"📁 Images folder: {images_folder}")
    print(f"📄 Output file: {output_file}")
    print(f"📊 Processing pages: {start_page} to {end_page}")
    print(f"⏱️ Delay between calls: {delay_seconds} seconds")
    print()
    
    # Check if images folder exists
    if not os.path.exists(images_folder):
        print(f"❌ Error: Images folder '{images_folder}' not found!")
        return
    
    # Start processing
    print("🔄 Starting processing...")
    start_time = time.time()
    
    try:
        results = processor.process_all_images(
            images_folder=images_folder,
            output_file=output_file,
            start_page=start_page,
            end_page=end_page,
            delay_seconds=delay_seconds
        )
        
        # Calculate processing time
        end_time = time.time()
        total_time = end_time - start_time
        minutes = int(total_time // 60)
        seconds = int(total_time % 60)
        
        # Print final summary
        print("\n" + "=" * 50)
        print("🎉 PROCESSING COMPLETED!")
        print("=" * 50)
        print(f"⏱️ Total time: {minutes}m {seconds}s")
        print(f"📊 Results summary:")
        print(f"   ✅ Successful: {results['summary']['successful']}")
        print(f"   ❌ Failed: {results['summary']['failed']}")
        print(f"   🔧 JSON Errors: {results['summary']['json_errors']}")
        print(f"   🌐 API Errors: {results['summary']['api_errors']}")
        print(f"📄 Output saved to: {output_file}")
        
        # Calculate success rate
        total_processed = results['summary']['successful'] + results['summary']['failed']
        if total_processed > 0:
            success_rate = (results['summary']['successful'] / total_processed) * 100
            print(f"📈 Success rate: {success_rate:.1f}%")
        
        # Show some sample data if available
        if results['extracted_data']:
            print(f"\n📝 Sample extracted data (first entry):")
            first_entry = results['extracted_data'][0]
            if 'document_info' in first_entry:
                doc_info = first_entry['document_info']
                print(f"   Chapter: {doc_info.get('chapter', 'N/A')}")
                print(f"   Article: {doc_info.get('article', 'N/A')}")
                print(f"   Title: {doc_info.get('title', 'N/A')}")
            
            if 'sections' in first_entry and first_entry['sections']:
                first_section = first_entry['sections'][0]
                print(f"   First section: {first_section.get('section_number', 'N/A')} - {first_section.get('title', 'N/A')}")
        
        print(f"\n💡 Next steps:")
        print(f"   - Review the results in '{output_file}'")
        print(f"   - If satisfied with quality, process remaining images")
        print(f"   - Check error details for any failed images")
        
    except KeyboardInterrupt:
        print("\n⚠️ Processing interrupted by user")
        print("💾 Partial results may have been saved")
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        print("🔍 Check the error details above")

if __name__ == "__main__":
    main()
